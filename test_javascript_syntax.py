#!/usr/bin/env python3
"""
快速测试JavaScript语法问题
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_reports_template_rendering():
    """测试报告模板渲染，特别是包含特殊字符的项目名称"""
    print("测试报告模板渲染...")
    
    try:
        from app import create_app
        from flask import render_template
        
        app = create_app()
        
        with app.app_context():
            with app.test_request_context():
                # 测试包含特殊字符的项目名称
                mock_reports = [
                    {
                        'id': 'test-id-1',
                        'project_name': "Test Project with 'single quotes'",
                        'creator_name': 'Test Creator',
                        'official_website': 'https://example.com',
                        'is_published': True,
                        'created_at': '2024-01-15T10:30:00Z',
                        'description': 'Test description'
                    },
                    {
                        'id': 'test-id-2',
                        'project_name': 'Test Project with "double quotes"',
                        'creator_name': 'Test Creator 2',
                        'official_website': 'https://example2.com',
                        'is_published': False,
                        'created_at': '2024-01-16T10:30:00Z',
                        'description': 'Another test description'
                    },
                    {
                        'id': 'test-id-3',
                        'project_name': "Complex 'name' with \"mixed\" quotes & symbols!",
                        'creator_name': 'Test Creator 3',
                        'official_website': 'https://example3.com',
                        'is_published': True,
                        'created_at': '2024-01-17T10:30:00Z',
                        'description': 'Complex test description'
                    }
                ]
                
                mock_pagination = {
                    'total': 3,
                    'total_pages': 1,
                    'page': 1,
                    'has_prev': False,
                    'has_next': False
                }
                
                # 渲染模板
                rendered = render_template('admin/reports.html', 
                                         reports=mock_reports, 
                                         pagination=mock_pagination)
                
                # 检查是否包含data属性而不是onclick中的复杂参数
                if 'data-report-id="test-id-1"' in rendered:
                    print("✓ 使用data属性传递报告ID")
                else:
                    print("✗ 未找到data-report-id属性")
                    return False
                
                if 'data-project-name="Test Project with &#39;single quotes&#39;"' in rendered:
                    print("✓ 正确处理包含单引号的项目名称（HTML转义）")
                else:
                    print("✗ 未正确处理包含单引号的项目名称")
                    # 打印实际内容用于调试
                    import re
                    matches = re.findall(r'data-project-name="([^"]*?)"', rendered)
                    print(f"实际找到的data-project-name值: {matches}")
                    return False
                
                # 检查onclick属性是否简化
                if 'onclick="deleteReport(this)"' in rendered:
                    print("✓ 使用简化的onclick属性")
                else:
                    print("✗ onclick属性未简化")
                    return False
                
                # 检查是否没有复杂的JSON在onclick中
                if 'onclick="deleteReport(' not in rendered or '"single quotes"' not in rendered:
                    print("✓ onclick中没有复杂的参数")
                else:
                    print("✗ onclick中仍有复杂参数")
                    return False
                
                print("✓ 报告模板渲染测试通过")
                return True
                
    except Exception as e:
        print(f"✗ 报告模板渲染测试失败: {e}")
        return False

def test_javascript_functions():
    """测试JavaScript函数定义"""
    print("测试JavaScript函数定义...")
    
    try:
        with open('templates/admin/reports.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查函数定义
        required_functions = [
            'function editReport(button)',
            'function deleteReport(button)',
            'function publishReport(button)',
            'function unpublishReport(button)',
            'function updateReportStatus(button, reportId, isPublished, actionName)',
            'function getCSRFToken()'
        ]
        
        for func in required_functions:
            if func in content:
                print(f"✓ 找到函数: {func}")
            else:
                print(f"✗ 缺少函数: {func}")
                return False
        
        # 检查是否使用了data属性
        if 'getAttribute(\'data-report-id\')' in content:
            print("✓ 使用data属性获取报告ID")
        else:
            print("✗ 未使用data属性获取报告ID")
            return False
        
        if 'getAttribute(\'data-project-name\')' in content:
            print("✓ 使用data属性获取项目名称")
        else:
            print("✗ 未使用data属性获取项目名称")
            return False
        
        print("✓ JavaScript函数定义测试通过")
        return True
        
    except Exception as e:
        print(f"✗ JavaScript函数测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("JavaScript语法修复验证")
    print("=" * 50)
    
    tests = [
        ("模板渲染测试", test_reports_template_rendering),
        ("JavaScript函数测试", test_javascript_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 JavaScript语法修复成功！")
        print("\n✅ 修复内容:")
        print("  - 使用data属性代替onclick参数")
        print("  - 避免在HTML属性中使用复杂JSON")
        print("  - 简化函数调用，传递button元素")
        print("  - 从data属性中安全获取数据")
        
        print("\n🚀 现在删除功能应该正常工作了！")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
